/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #fafafa;
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding-top: 80px; /* Account for fixed navbar */
}

/* Container */
.container {
  /* max-width: 1200px; */
  margin: 0 auto;
  padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }

p {
  margin-bottom: 1rem;
  color: #666;
}

/* Links */
a {
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: white;
  color: #333;
  border: 2px solid #e1e5e9;
}

.btn-secondary:hover {
  border-color: #667eea;
  color: #667eea;
}

/* Loading States */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.loading-spinner {
  padding: 20px;
  font-size: 18px;
  color: #667eea;
}

/* Error States */
.error-container {
  text-align: center;
  padding: 40px 20px;
}

.error-message {
  background: #fee;
  color: #c33;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #c33;
}

.success-message {
  background: #efe;
  color: #363;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #363;
}

/* Navbar Styles */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: white;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
}

.navbar-logo {
  font-size: 24px;
  font-weight: 700;
  color: #667eea;
  text-decoration: none;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 40px;
}

.navbar-menu {
  display: flex;
  gap: 32px;
}

.navbar-link {
  color: #333;
  font-weight: 500;
  padding: 8px 0;
  position: relative;
  transition: color 0.3s ease;
}

.navbar-link:hover {
  color: #667eea;
}

.navbar-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: #667eea;
  transition: width 0.3s ease;
}

.navbar-link:hover::after {
  width: 100%;
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.navbar-cart {
  position: relative;
  padding: 8px;
  border-radius: 8px;
  transition: background 0.3s ease;
}

.navbar-cart:hover {
  background: #f8f9fa;
}

.navbar-icon {
  width: 24px;
  height: 24px;
  color: #333;
}

.cart-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: #ff4757;
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 16px;
}

.auth-links {
  display: flex;
  align-items: center;
  gap: 16px;
}

.navbar-button {
  background: #667eea;
  color: white;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.navbar-button:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

.mobile-menu-button {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
}

.mobile-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.mobile-menu-link,
.mobile-menu-button {
  display: block;
  padding: 12px 0;
  color: #333;
  font-weight: 500;
  border-bottom: 1px solid #eee;
  background: none;
  border: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
}

.mobile-menu-button {
  background: #667eea;
  color: white;
  border-radius: 6px;
  margin-top: 10px;
}

@media (max-width: 768px) {
  .navbar-right {
    display: none;
  }

  .mobile-menu-button {
    display: block;
  }

  .mobile-menu {
    display: block;
  }
}

/* Home Page Styles */
.home {
  overflow-x: hidden;
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 100px 0;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="0,0 1000,0 1000,100 0,80"/></svg>');
  background-size: cover;
}

.hero-content {
  /* max-width: 1200px; */
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  position: relative;
  z-index: 2;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 24px;
  background: linear-gradient(45deg, #fff, #e2e8f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  margin-bottom: 32px;
  color: rgba(255, 255, 255, 0.9);
}

.hero-button {
  display: inline-block;
  background: white;
  color: #667eea;
  padding: 16px 32px;
  border-radius: 50px;
  font-weight: 600;
  font-size: 18px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(255, 255, 255, 0.3);
}

.hero-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(255, 255, 255, 0.4);
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-placeholder {
  width: 400px;
  height: 300px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 4rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Section Styles */
.section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 60px;
  color: #2d3748;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 2px;
}

/* Categories Section */
.categories-section {
  padding: 100px 0;
  background: white;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin-top: 60px;
}

.category-card {
  background: white;
  padding: 40px 30px;
  border-radius: 20px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
}

.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.category-card:hover::before {
  transform: scaleX(1);
}

.category-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.category-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.category-name {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: #2d3748;
}

.category-description {
  color: #718096;
  margin-bottom: 16px;
  line-height: 1.6;
}

.category-count {
  color: #667eea;
  font-weight: 500;
  font-size: 14px;
}

/* Featured Products Section */
.featured-section {
  padding: 100px 0;
  background: #f8fafc;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 60px;
}

.product-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  position: relative;
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.product-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
  position: relative;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea20, #764ba220);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: #667eea;
}

.product-info {
  padding: 24px;
}

.product-name {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #2d3748;
}

.product-price {
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 8px;
}

.product-scent {
  color: #718096;
  font-size: 14px;
  font-style: italic;
}

.featured-footer {
  text-align: center;
  margin-top: 60px;
}

.view-all-button {
  display: inline-block;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 16px 32px;
  border-radius: 50px;
  font-weight: 600;
  font-size: 18px;
  transition: all 0.3s ease;
}

.view-all-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Features Section */
.features-section {
  padding: 100px 0;
  background: white;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.feature-card {
  text-align: center;
  padding: 40px 20px;
  border-radius: 16px;
  transition: all 0.3s ease;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
}

.feature-card:hover {
  background: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 24px;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: #2d3748;
}

.feature-description {
  color: #718096;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-placeholder {
    width: 300px;
    height: 200px;
    font-size: 2.5rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .categories-grid,
  .products-grid,
  .features-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .container {
    padding: 0 16px;
  }
}

/* Additional Styles for Forms and Other Pages */
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.auth-container {
  width: 100%;
  max-width: 500px;
}

.auth-card {
  background: white;
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.auth-header {
  text-align: center;
  margin-bottom: 30px;
}

.auth-header h1 {
  color: #2d3748;
  margin-bottom: 8px;
}

.auth-header p {
  color: #718096;
}

.auth-form {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2d3748;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
}

.form-input.disabled {
  background: #f7fafc;
  color: #718096;
}

.auth-button {
  width: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 14px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.auth-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.auth-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.auth-footer {
  text-align: center;
  margin-top: 20px;
}

.auth-footer a {
  color: #667eea;
  font-weight: 500;
}

.demo-accounts {
  margin-top: 30px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.demo-accounts h3 {
  color: #2d3748;
  margin-bottom: 16px;
  font-size: 16px;
}

.demo-account {
  margin-bottom: 12px;
  font-size: 14px;
  color: #4a5568;
}

/* Product Detail Styles */
.product-detail {
  padding: 40px 0;
}

.back-button {
  background: #f7fafc;
  color: #4a5568;
  padding: 8px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  margin-bottom: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: #edf2f7;
  color: #2d3748;
}

.product-detail-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: start;
}

.product-image-large {
  width: 100%;
  height: 500px;
  object-fit: cover;
  border-radius: 16px;
}

.product-placeholder-large {
  width: 100%;
  height: 500px;
  background: linear-gradient(135deg, #667eea20, #764ba220);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 6rem;
  color: #667eea;
}

.product-title {
  font-size: 2.5rem;
  color: #2d3748;
  margin-bottom: 16px;
}

.product-price-large {
  font-size: 2rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 20px;
}

.product-detail-item {
  margin-bottom: 12px;
  color: #4a5568;
}

.product-description,
.product-ingredients {
  margin: 30px 0;
}

.product-description h3,
.product-ingredients h3 {
  color: #2d3748;
  margin-bottom: 12px;
}

.in-stock {
  color: #38a169;
  font-weight: 500;
}

.out-of-stock {
  color: #e53e3e;
  font-weight: 500;
}

.add-to-cart-section {
  margin-top: 40px;
  padding: 30px;
  background: #f8fafc;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
}

.quantity-selector {
  margin-bottom: 20px;
}

.quantity-selector label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2d3748;
}

.quantity-select {
  padding: 8px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  font-size: 16px;
}

.add-to-cart-button-large {
  width: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 16px;
  border: none;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-to-cart-button-large:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }

  .product-detail-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .product-title {
    font-size: 2rem;
  }

  .product-image-large,
  .product-placeholder-large {
    height: 300px;
  }

  .product-placeholder-large {
    font-size: 4rem;
  }
}

/* Products Page Styles */
.products-page {
  padding: 40px 0;
}

.products-header {
  text-align: center;
  margin-bottom: 60px;
}

.products-header h1 {
  font-size: 2.5rem;
  color: #2d3748;
  margin-bottom: 16px;
}

.filters-section {
  margin-bottom: 40px;
  padding: 30px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.filters-row {
  display: grid;
  grid-template-columns: 2fr 1fr auto;
  gap: 20px;
  align-items: end;
}

.filter-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2d3748;
}

.filter-input,
.filter-select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: #667eea;
}

.clear-filters-button {
  background: #f7fafc;
  color: #4a5568;
  padding: 12px 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.clear-filters-button:hover {
  background: #edf2f7;
  color: #2d3748;
}

.products-count {
  margin-bottom: 30px;
  color: #718096;
  font-size: 16px;
}

.no-products {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.no-products h3 {
  color: #2d3748;
  margin-bottom: 12px;
}

.product-link {
  display: block;
  color: inherit;
}

.product-actions {
  padding: 20px 24px;
  border-top: 1px solid #e2e8f0;
}

.add-to-cart-button {
  width: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 12px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-to-cart-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.add-to-cart-button:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
}

.product-weight,
.product-category {
  color: #718096;
  font-size: 14px;
  margin-bottom: 4px;
}

/* Cart Page Styles */
.cart-page {
  padding: 40px 0;
  min-height: 60vh;
}

.cart-page h1 {
  font-size: 2.5rem;
  color: #2d3748;
  margin-bottom: 40px;
}

.auth-required {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.login-button {
  display: inline-block;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  margin-top: 20px;
}

.empty-cart {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.shop-button {
  display: inline-block;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 16px 32px;
  border-radius: 50px;
  font-weight: 600;
  margin-top: 20px;
}

.cart-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  align-items: start;
}

.cart-items {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.cart-item {
  display: grid;
  grid-template-columns: 120px 1fr auto auto auto;
  gap: 20px;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
}

.cart-item:last-child {
  border-bottom: none;
}

.item-image {
  width: 120px;
  height: 120px;
  border-radius: 12px;
  overflow: hidden;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea20, #764ba220);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: #667eea;
}

.item-details h3 {
  color: #2d3748;
  margin-bottom: 8px;
}

.item-details a:hover {
  color: #667eea;
}

.item-price {
  color: #718096;
  margin-bottom: 8px;
}

.low-stock {
  color: #e53e3e;
  font-size: 14px;
  font-weight: 500;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.quantity-button {
  width: 32px;
  height: 32px;
  border: 1px solid #e2e8f0;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s ease;
}

.quantity-button:hover:not(:disabled) {
  border-color: #667eea;
  color: #667eea;
}

.quantity-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-display {
  font-weight: 600;
  min-width: 30px;
  text-align: center;
}

.subtotal-price {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
}

.remove-button {
  background: #fed7d7;
  color: #c53030;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.remove-button:hover {
  background: #feb2b2;
}

.cart-summary {
  position: sticky;
  top: 100px;
}

.summary-card {
  background: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.summary-card h3 {
  color: #2d3748;
  margin-bottom: 24px;
  font-size: 1.5rem;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  color: #4a5568;
}

.total-row {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  padding-top: 16px;
  border-top: 2px solid #e2e8f0;
  margin-top: 16px;
}

.checkout-button {
  width: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 16px;
  border: none;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  margin: 24px 0 16px 0;
  transition: all 0.3s ease;
}

.checkout-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.continue-shopping {
  display: block;
  text-align: center;
  color: #667eea;
  font-weight: 500;
}

@media (max-width: 768px) {
  .filters-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .cart-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .cart-item {
    grid-template-columns: 80px 1fr;
    gap: 16px;
  }

  .item-image {
    width: 80px;
    height: 80px;
  }

  .item-quantity,
  .item-subtotal,
  .item-actions {
    grid-column: 1 / -1;
    margin-top: 12px;
  }

  .quantity-controls {
    justify-content: flex-start;
  }
}
