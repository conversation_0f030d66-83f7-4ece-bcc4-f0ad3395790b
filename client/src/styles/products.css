/* Products Page Styles */
.products-page {
  padding: 40px 0;
}

.products-header {
  text-align: center;
  margin-bottom: 60px;
}

.products-header h1 {
  font-size: 2.5rem;
  color: #2d3748;
  margin-bottom: 16px;
}

.filters-section {
  margin-bottom: 40px;
  padding: 30px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.filters-row {
  display: grid;
  grid-template-columns: 2fr 1fr auto;
  gap: 20px;
  align-items: end;
}

.filter-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2d3748;
}

.filter-input,
.filter-select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: #667eea;
}

.clear-filters-button {
  background: #f7fafc;
  color: #4a5568;
  padding: 12px 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.clear-filters-button:hover {
  background: #edf2f7;
  color: #2d3748;
}

.products-count {
  margin-bottom: 30px;
  color: #718096;
  font-size: 16px;
}

.no-products {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.no-products h3 {
  color: #2d3748;
  margin-bottom: 12px;
}

.product-link {
  display: block;
  color: inherit;
}

.product-actions {
  padding: 20px 24px;
  border-top: 1px solid #e2e8f0;
}

.add-to-cart-button {
  width: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 12px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-to-cart-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.add-to-cart-button:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
}

.product-weight,
.product-category {
  color: #718096;
  font-size: 14px;
  margin-bottom: 4px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .filters-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
