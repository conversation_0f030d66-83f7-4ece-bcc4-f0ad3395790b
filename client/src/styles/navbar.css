/* Navbar Styles */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: white;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
}

.navbar-logo {
  font-size: 24px;
  font-weight: 700;
  color: #667eea;
  text-decoration: none;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.navbar-menu {
  display: flex;
  gap: 32px;
}

.navbar-link {
  color: #333;
  font-weight: 500;
  padding: 8px 0;
  position: relative;
  transition: color 0.3s ease;
}

.navbar-link:hover {
  color: #667eea;
}

.navbar-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: #667eea;
  transition: width 0.3s ease;
}

.navbar-link:hover::after {
  width: 100%;
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.navbar-cart {
  width: 18%;
  position: relative;
  padding: 8px;
  border-radius: 8px;
  transition: background 0.3s ease;
}

.navbar-cart:hover {
  background: #f8f9fa;
}

.navbar-icon {
  width: 24px;
  height: 24px;
  color: #333;
}

.cart-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: #ff4757;
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 16px;
}

.auth-links {
  display: flex;
  align-items: center;
  gap: 16px;
}

.navbar-button {
  background: #667eea;
  color: white;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.navbar-button:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

.mobile-menu-button {
  display: none !important;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
}

.mobile-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.mobile-menu-link,
.mobile-menu .mobile-menu-button {
  display: block;
  padding: 12px 0;
  color: #333;
  font-weight: 500;
  border-bottom: 1px solid #eee;
  background: none;
  border: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
}

.mobile-menu .mobile-menu-button {
  background: #667eea;
  color: white;
  border-radius: 6px;
  margin-top: 10px;
}

@media (max-width: 480px) {
  .navbar-right {
    display: none;
  }

  .mobile-menu-button {
    display: block !important;
  }

  .mobile-menu {
    display: block;
  }
}
