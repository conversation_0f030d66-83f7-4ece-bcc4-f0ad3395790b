/* Cart Page Styles */
.cart-page {
  padding: 40px 0;
  min-height: 60vh;
}

.cart-page h1 {
  font-size: 2.5rem;
  color: #2d3748;
  margin-bottom: 40px;
}

.auth-required {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.login-button {
  display: inline-block;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  margin-top: 20px;
}

.empty-cart {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.shop-button {
  display: inline-block;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 16px 32px;
  border-radius: 50px;
  font-weight: 600;
  margin-top: 20px;
}

.cart-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  align-items: start;
}

.cart-items {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.cart-item {
  display: grid;
  grid-template-columns: 120px 1fr auto auto auto;
  gap: 20px;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
}

.cart-item:last-child {
  border-bottom: none;
}

.item-image {
  width: 120px;
  height: 120px;
  border-radius: 12px;
  overflow: hidden;
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea20, #764ba220);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: #667eea;
}

.item-details h3 {
  color: #2d3748;
  margin-bottom: 8px;
}

.item-details a:hover {
  color: #667eea;
}

.item-price {
  color: #718096;
  margin-bottom: 8px;
}

.low-stock {
  color: #e53e3e;
  font-size: 14px;
  font-weight: 500;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.quantity-button {
  width: 32px;
  height: 32px;
  border: 1px solid #e2e8f0;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s ease;
}

.quantity-button:hover:not(:disabled) {
  border-color: #667eea;
  color: #667eea;
}

.quantity-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-display {
  font-weight: 600;
  min-width: 30px;
  text-align: center;
}

.subtotal-price {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
}

.remove-button {
  background: #fed7d7;
  color: #c53030;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.remove-button:hover {
  background: #feb2b2;
}

.cart-summary {
  position: sticky;
  top: 100px;
}

.summary-card {
  background: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.summary-card h3 {
  color: #2d3748;
  margin-bottom: 24px;
  font-size: 1.5rem;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  color: #4a5568;
}

.total-row {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  padding-top: 16px;
  border-top: 2px solid #e2e8f0;
  margin-top: 16px;
}

.checkout-button {
  width: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 16px;
  border: none;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  margin: 24px 0 16px 0;
  transition: all 0.3s ease;
}

.checkout-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.continue-shopping {
  display: block;
  text-align: center;
  color: #667eea;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .cart-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .cart-item {
    grid-template-columns: 80px 1fr;
    gap: 16px;
  }

  .item-image {
    width: 80px;
    height: 80px;
  }

  .item-quantity,
  .item-subtotal,
  .item-actions {
    grid-column: 1 / -1;
    margin-top: 12px;
  }

  .quantity-controls {
    justify-content: flex-start;
  }
}
