# Stylesheet Organization

This folder contains all the CSS styles for the application, organized by component and page.

## File Structure

```
styles/
├── index.css           # Main stylesheet that imports all others
├── base.css           # Base styles (reset, typography, buttons, loading states)
├── navbar.css         # Navigation bar component styles
├── home.css           # Home page styles (hero, categories, features)
├── auth.css           # Authentication pages (login, register)
├── products.css       # Products listing page styles
├── product-detail.css # Individual product detail page styles
├── cart.css           # Shopping cart page styles
└── README.md          # This documentation file
```

## Usage

The main `index.css` file imports all the individual stylesheets and is imported in `App.jsx`. This provides a clean separation of concerns while maintaining a single entry point for styles.

## Style Categories

### Base Styles (`base.css`)
- CSS reset and normalization
- Typography (headings, paragraphs, links)
- Button styles (primary, secondary)
- Loading and error states
- Container and layout utilities

### Component Styles
- **Navbar** (`navbar.css`): Navigation bar, mobile menu, authentication links

### Page Styles
- **Home** (`home.css`): Hero section, categories grid, featured products, features section
- **Auth** (`auth.css`): Login and register page forms and layouts
- **Products** (`products.css`): Product listing, filters, product cards
- **Product Detail** (`product-detail.css`): Individual product pages with image gallery and purchase options
- **Cart** (`cart.css`): Shopping cart items, quantity controls, checkout summary

## Responsive Design

Each stylesheet includes its own responsive breakpoints and mobile-specific styles using `@media (max-width: 768px)` queries.

## Maintenance

When adding new components or pages:
1. Create a new CSS file in this directory
2. Add the import to `index.css`
3. Follow the existing naming conventions and structure
4. Include responsive styles in the same file
