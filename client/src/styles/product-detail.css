/* Product Detail Styles */
.product-detail {
  padding: 40px 0;
}

.back-button {
  background: #f7fafc;
  color: #4a5568;
  padding: 8px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  margin-bottom: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: #edf2f7;
  color: #2d3748;
}

.product-detail-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: start;
}

.product-image-large {
  width: 100%;
  height: 500px;
  object-fit: cover;
  border-radius: 16px;
}

.product-placeholder-large {
  width: 100%;
  height: 500px;
  background: linear-gradient(135deg, #667eea20, #764ba220);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 6rem;
  color: #667eea;
}

.product-title {
  font-size: 2.5rem;
  color: #2d3748;
  margin-bottom: 16px;
}

.product-price-large {
  font-size: 2rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 20px;
}

.product-detail-item {
  margin-bottom: 12px;
  color: #4a5568;
}

.product-description,
.product-ingredients {
  margin: 30px 0;
}

.product-description h3,
.product-ingredients h3 {
  color: #2d3748;
  margin-bottom: 12px;
}

.in-stock {
  color: #38a169;
  font-weight: 500;
}

.out-of-stock {
  color: #e53e3e;
  font-weight: 500;
}

.add-to-cart-section {
  margin-top: 40px;
  padding: 30px;
  background: #f8fafc;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
}

.quantity-selector {
  margin-bottom: 20px;
}

.quantity-selector label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #2d3748;
}

.quantity-select {
  padding: 8px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  font-size: 16px;
}

.add-to-cart-button-large {
  width: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 16px;
  border: none;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-to-cart-button-large:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .product-detail-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .product-title {
    font-size: 2rem;
  }

  .product-image-large,
  .product-placeholder-large {
    height: 300px;
  }

  .product-placeholder-large {
    font-size: 4rem;
  }
}
