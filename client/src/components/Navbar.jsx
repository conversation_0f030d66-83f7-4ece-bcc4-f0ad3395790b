import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useCart } from '../contexts/CartContext';
import { ShoppingCartIcon, UserIcon, Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { user, logout, isAuthenticated } = useAuth();
  const { getCartItemCount } = useCart();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/');
    setIsMenuOpen(false);
  };

  const cartItemCount = getCartItemCount();

  return (
    <nav className="navbar">
      <div className="navbar-container">
        {/* Logo */}
        <Link to="/" className="navbar-logo">
          🧼 SoapStore
        </Link>

        {/* Desktop Navigation and Actions */}
        <div className="navbar-right">
          <div className="navbar-menu">
            <Link to="/" className="navbar-link">Home</Link>
            <Link to="/products" className="navbar-link">Products</Link>
          </div>

          {/* Desktop Actions */}
          <div className="navbar-actions">
          {/* Cart */}
          <Link to="/cart" className="navbar-cart">
            <ShoppingCartIcon className="navbar-icon" />
            {cartItemCount > 0 && (
              <span className="cart-badge">{cartItemCount}</span>
            )}
          </Link>

          {/* User Menu */}
          {isAuthenticated ? (
            <div className="user-menu">
              <Link to="/profile" className="navbar-link">
                <UserIcon className="navbar-icon" />
                {user?.firstName}
              </Link>
              <button onClick={handleLogout} className="navbar-button">
                Logout
              </button>
            </div>
          ) : (
            <div className="auth-links">
              <Link to="/login" className="navbar-link">Login</Link>
              <Link to="/register" className="navbar-button">Register</Link>
            </div>
          )}
        </div>
        </div>

        {/* Mobile Menu Button */}
        <button
          className="mobile-menu-button"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
        >
          {isMenuOpen ? (
            <XMarkIcon className="navbar-icon" />
          ) : (
            <Bars3Icon className="navbar-icon" />
          )}
        </button>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="mobile-menu">
          <Link 
            to="/" 
            className="mobile-menu-link"
            onClick={() => setIsMenuOpen(false)}
          >
            Home
          </Link>
          <Link 
            to="/products" 
            className="mobile-menu-link"
            onClick={() => setIsMenuOpen(false)}
          >
            Products
          </Link>
          <Link 
            to="/cart" 
            className="mobile-menu-link"
            onClick={() => setIsMenuOpen(false)}
          >
            Cart {cartItemCount > 0 && `(${cartItemCount})`}
          </Link>
          
          {isAuthenticated ? (
            <>
              <Link 
                to="/profile" 
                className="mobile-menu-link"
                onClick={() => setIsMenuOpen(false)}
              >
                Profile
              </Link>
              <button 
                onClick={handleLogout} 
                className="mobile-menu-button"
              >
                Logout
              </button>
            </>
          ) : (
            <>
              <Link 
                to="/login" 
                className="mobile-menu-link"
                onClick={() => setIsMenuOpen(false)}
              >
                Login
              </Link>
              <Link 
                to="/register" 
                className="mobile-menu-button"
                onClick={() => setIsMenuOpen(false)}
              >
                Register
              </Link>
            </>
          )}
        </div>
      )}
    </nav>
  );
};

export default Navbar;
