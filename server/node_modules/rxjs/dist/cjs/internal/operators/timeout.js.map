{"version": 3, "file": "timeout.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/timeout.ts"], "names": [], "mappings": ";;;AAAA,4CAAoD;AAEpD,yCAA6C;AAE7C,qCAAuC;AAEvC,qDAAoD;AACpD,6DAA4D;AAC5D,2DAAgE;AAChE,2DAA0D;AA4E7C,QAAA,YAAY,GAAqB,mCAAgB,CAC5D,UAAC,MAAM;IACL,OAAA,SAAS,gBAAgB,CAAY,IAAoC;QAApC,qBAAA,EAAA,WAAoC;QACvE,MAAM,CAAC,IAAI,CAAC,CAAC;QACb,IAAI,CAAC,OAAO,GAAG,sBAAsB,CAAC;QACtC,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC;QAC3B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;AALD,CAKC,CACJ,CAAC;AA6MF,SAAgB,OAAO,CACrB,MAA8C,EAC9C,YAA4B;IAStB,IAAA,KAMF,CAAC,oBAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,MAAM,CAA2B,EAL9H,KAAK,WAAA,EACL,IAAI,UAAA,EACJ,YAAiC,EAA3B,KAAK,mBAAG,mBAAmB,KAAA,EACjC,iBAA0C,EAA1C,SAAS,mBAAG,YAAY,aAAZ,YAAY,cAAZ,YAAY,GAAI,sBAAc,KAAA,EAC1C,YAAY,EAAZ,IAAI,mBAAG,IAAK,KACkH,CAAC;IAEjI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;QAEjC,MAAM,IAAI,SAAS,CAAC,sBAAsB,CAAC,CAAC;KAC7C;IAED,OAAO,cAAO,CAAC,UAAC,MAAM,EAAE,UAAU;QAMhC,IAAI,0BAAwC,CAAC;QAG7C,IAAI,iBAA+B,CAAC;QAGpC,IAAI,SAAS,GAAa,IAAI,CAAC;QAG/B,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,IAAM,UAAU,GAAG,UAAC,KAAa;YAC/B,iBAAiB,GAAG,iCAAe,CACjC,UAAU,EACV,SAAS,EACT;gBACE,IAAI;oBACF,0BAA0B,CAAC,WAAW,EAAE,CAAC;oBACzC,qBAAS,CACP,KAAM,CAAC;wBACL,IAAI,MAAA;wBACJ,SAAS,WAAA;wBACT,IAAI,MAAA;qBACL,CAAC,CACH,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;iBACzB;gBAAC,OAAO,GAAG,EAAE;oBACZ,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;iBACvB;YACH,CAAC,EACD,KAAK,CACN,CAAC;QACJ,CAAC,CAAC;QAEF,0BAA0B,GAAG,MAAM,CAAC,SAAS,CAC3C,6CAAwB,CACtB,UAAU,EACV,UAAC,KAAQ;YAEP,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,WAAW,EAAE,CAAC;YACjC,IAAI,EAAE,CAAC;YAEP,UAAU,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC;YAErC,IAAK,GAAG,CAAC,IAAI,UAAU,CAAC,IAAK,CAAC,CAAC;QACjC,CAAC,EACD,SAAS,EACT,SAAS,EACT;YACE,IAAI,CAAC,CAAA,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,MAAM,CAAA,EAAE;gBAC9B,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,WAAW,EAAE,CAAC;aAClC;YAGD,SAAS,GAAG,IAAI,CAAC;QACnB,CAAC,CACF,CACF,CAAC;QAQF,CAAC,IAAI,IAAI,UAAU,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,SAAU,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAK,CAAC,CAAC;IAC/G,CAAC,CAAC,CAAC;AACL,CAAC;AA/FD,0BA+FC;AAOD,SAAS,mBAAmB,CAAC,IAAsB;IACjD,MAAM,IAAI,oBAAY,CAAC,IAAI,CAAC,CAAC;AAC/B,CAAC"}